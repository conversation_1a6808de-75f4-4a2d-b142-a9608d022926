{"rustc": 4723136837156968084, "features": "[\"http\", \"unicode\"]", "declared_features": "[\"default\", \"http\", \"unicode\"]", "target": 5816441226683462542, "profile": 14325600829487057851, "path": 13452868895016725658, "deps": [[4405182208873388884, "http", false, 14793202608953780216], [6227569293941247354, "bytestring", false, 18194830187363600357], [8606274917505247608, "tracing", false, 16080499271145701366], [9451456094439810778, "regex", false, 9758240011634469845], [9689903380558560274, "serde", false, 17577479795719911176], [10411997081178400487, "cfg_if", false, 15889621798505669805], [16224642111061375652, "regex_lite", false, 11026611970609552859]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/actix-router-4ed6032307f88ebb/dep-lib-actix_router", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}