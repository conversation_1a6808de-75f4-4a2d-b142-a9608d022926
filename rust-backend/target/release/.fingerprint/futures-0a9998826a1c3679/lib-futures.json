{"rustc": 4723136837156968084, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 1415802117242499782, "path": 10496418484301975030, "deps": [[5103565458935487, "futures_io", false, 8596384846362731822], [1811549171721445101, "futures_channel", false, 17938127759904161808], [7013762810557009322, "futures_sink", false, 8706281374908161071], [7620660491849607393, "futures_core", false, 17146092250928634476], [10629569228670356391, "futures_util", false, 9718479508727985384], [12779779637805422465, "futures_executor", false, 1098168551422020060], [16240732885093539806, "futures_task", false, 6128378073380521777]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/futures-0a9998826a1c3679/dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}