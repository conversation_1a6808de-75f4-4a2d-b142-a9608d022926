{"rustc": 4723136837156968084, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 14325600829487057851, "path": 11832897722978481492, "deps": [[5466618496199522463, "crc32fast", false, 9810627817973662147], [16126238571916867475, "miniz_oxide", false, 16492575241081953527]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/flate2-01d465c1ec4d955e/dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}