{"rustc": 4723136837156968084, "features": "[\"alloc-stdlib\", \"default\", \"std\"]", "declared_features": "[\"alloc-stdlib\", \"benchmark\", \"billing\", \"default\", \"disable-timer\", \"disallow_large_window_size\", \"external-literal-probability\", \"ffi-api\", \"float64\", \"floating_point_context_mixing\", \"no-stdlib-ffi-binding\", \"pass-through-ffi-panics\", \"seccomp\", \"sha2\", \"simd\", \"std\", \"validation\", \"vector_scratch_space\"]", "target": 7073890835992331790, "profile": 14325600829487057851, "path": 12618886623001365232, "deps": [[9611597350722197978, "alloc_no_stdlib", false, 1337768154159508791], [12023582763595948122, "brotli_decompressor", false, 4697086603486543620], [17470296833448545982, "alloc_stdlib", false, 7468554001108224215]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/brotli-b783fcc5d1b62468/dep-lib-brotli", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}