{"rustc": 4723136837156968084, "features": "[\"default\", \"runtime-detection\", \"serde\", \"serde_impl\", \"serde_json\", \"swar-number-parsing\"]", "declared_features": "[\"128bit\", \"ahash\", \"alloc\", \"alloc_counter\", \"approx-number-parsing\", \"arraybackend\", \"beef\", \"bench-all\", \"bench-apache_builds\", \"bench-canada\", \"bench-citm_catalog\", \"bench-event_stacktrace_10kb\", \"bench-github_events\", \"bench-log\", \"bench-serde\", \"bench-twitter\", \"big-int-as-float\", \"colored\", \"default\", \"docsrs\", \"getopts\", \"hints\", \"jemallocator\", \"known-key\", \"no-inline\", \"once_cell\", \"ordered-float\", \"perf\", \"perfcnt\", \"runtime-detection\", \"serde\", \"serde_impl\", \"serde_json\", \"swar-number-parsing\", \"value-no-dup-keys\"]", "target": 10950010384242988235, "profile": 7618518062762958000, "path": 17664448795460626511, "deps": [[8067010153367330186, "simdutf8", false, 13846888232373681724], [9689903380558560274, "serde", false, 17577479795719911176], [13213049336807611471, "value_trait", false, 11951186518665581907], [13535282280064720520, "ref_cast", false, 6672688514882960718], [15184931719406785749, "halfbrown", false, 9524742221800953321], [15367738274754116744, "serde_json", false, 6434973673477111295]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/simd-json-6500ef15d4e62fb7/dep-lib-simd_json", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}