{"rustc": 4723136837156968084, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 14325600829487057851, "path": 8541861006902314340, "deps": [[555019317135488525, "regex_automata", false, 7051832586120520830], [2779309023524819297, "aho_corasick", false, 775214326827973769], [3129130049864710036, "memchr", false, 14011000521357907412], [9408802513701742484, "regex_syntax", false, 12216899749423151896]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/regex-ca609c0b8ef24751/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}