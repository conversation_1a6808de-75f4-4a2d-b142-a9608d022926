#!/bin/bash
# deploy-terminal.sh

# Source common deployment functions
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../deployment-common.sh"

# Определение переменных окружения
ENVIRONMENT=${1:-staging}  # По умолчанию staging
if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
    echo "Usage: $0 [staging|production]"
    echo "Invalid environment: $ENVIRONMENT. Use 'staging' or 'production'"
    exit 1
fi

# Настройка переменных для разных окружений
if [[ "$ENVIRONMENT" == "production" ]]; then
    DOMAIN="quer.us"
    WEB_ROOT="/var/www/quer.us"
    SERVICE_NAME="astro-landing-production"
    BACKEND_SERVICE="quer-backend-production"
    TERMINAL_SERVICE="quer-terminal-backend-production"
    TERMINAL_PORT="8082"
    AUTH_USER="none"  # Без авторизации для production
    AUTH_PASS="none"
else
    DOMAIN="s.quer.us"
    WEB_ROOT="/var/www/s.quer.us"
    SERVICE_NAME="astro-landing-staging"
    BACKEND_SERVICE="quer-backend-staging"
    TERMINAL_SERVICE="quer-terminal-backend-staging"
    TERMINAL_PORT="8081"
    AUTH_USER="xxx"
    AUTH_PASS="eBqN28tyP"
fi

# Настройка цветного вывода для улучшения читаемости
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Функция для вывода сообщений с отметкой времени
log() {
  echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] [$ENVIRONMENT]${NC} $1"
}

# Функция для вывода сообщений об успехе
success() {
  echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] [$ENVIRONMENT]${NC} $1"
}

# Функция для вывода сообщений об ошибке
error() {
  echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] [$ENVIRONMENT]${NC} $1"
  exit 1
}

# Шаг 0: Очистка старой директории build
log "Очистка старой директории build..."
if [ -d "build" ]; then
  log "Найдена существующая директория build. Удаляем..."
  rm -rf build || error "Не удалось удалить старую директорию build!"
fi
success "Очистка директории build завершена!"

# Шаг 1: Сборка приложения
log "Начинаем сборку React-приложения..."
npm run build || error "Ошибка при сборке приложения!"
success "Сборка приложения успешно завершена!"

# Шаг 2: Проверка SSH-соединения
log "Проверка SSH-соединения..."
ssh quer-x0 "echo 'SSH подключение работает!'" || error "Не удалось подключиться к серверу. Проверьте SSH-ключ и соединение."
success "SSH-соединение работает!"

# Шаг 2.5: Создание/обновление скриптов-оберток на сервере
log "Создание/обновление скриптов-оберток на сервере..."
ssh quer-x0 "mkdir -p ~/deploy-scripts"

# Скрипт для очистки директории терминала
ssh quer-x0 "cat > ~/deploy-scripts/clean-public-terminal.sh << 'EOF'
#!/bin/bash
ENVIRONMENT=\$1
if [[ \"\$ENVIRONMENT\" == \"production\" ]]; then
  rm -rf /var/www/quer.us/current/public/terminal/*
else
  rm -rf /var/www/s.quer.us/current/public/terminal/*
fi
EOF
chmod +x ~/deploy-scripts/clean-public-terminal.sh"

# Скрипт для установки прав доступа
ssh quer-x0 "cat > ~/deploy-scripts/set-permissions.sh << 'EOF'
#!/bin/bash
ENVIRONMENT=\$1
if [[ \"\$ENVIRONMENT\" == \"production\" ]]; then
  find /var/www/quer.us/current/public -type d -exec chmod 755 {} \;
  find /var/www/quer.us/current/public -type f -exec chmod 644 {} \;
else
  find /var/www/s.quer.us/current/public -type d -exec chmod 755 {} \;
  find /var/www/s.quer.us/current/public -type f -exec chmod 644 {} \;
fi
EOF
chmod +x ~/deploy-scripts/set-permissions.sh"

# Скрипт для изменения владельца на www-data
ssh quer-x0 "cat > ~/deploy-scripts/set-owner.sh << 'EOF'
#!/bin/bash
ENVIRONMENT=\$1
if [[ \"\$ENVIRONMENT\" == \"production\" ]]; then
  chown -R www-data:www-data /var/www/quer.us/current/public
else
  chown -R www-data:www-data /var/www/s.quer.us/current/public
fi
EOF
chmod +x ~/deploy-scripts/set-owner.sh"

# Скрипт для изменения владельца на x0
ssh quer-x0 "cat > ~/deploy-scripts/set-owner-to-x0.sh << 'EOF'
#!/bin/bash
ENVIRONMENT=\$1
if [[ \"\$ENVIRONMENT\" == \"production\" ]]; then
  chown -R x0:x0 /var/www/quer.us/current/public
else
  chown -R x0:x0 /var/www/s.quer.us/current/public
fi
EOF
chmod +x ~/deploy-scripts/set-owner-to-x0.sh"

# Скрипт для перезапуска сервисов
ssh quer-x0 "cat > ~/deploy-scripts/restart-services.sh << 'EOF'
#!/bin/bash
ENVIRONMENT=\$1
systemctl reload nginx
if [[ \"\$ENVIRONMENT\" == \"production\" ]]; then
  systemctl restart quer-backend-production.service
  systemctl restart quer-terminal-backend-production.service
else
  systemctl restart quer-backend-staging.service
  systemctl restart quer-terminal-backend-staging.service
fi
EOF
chmod +x ~/deploy-scripts/restart-services.sh"

success "Скрипты успешно созданы/обновлены!"

# Шаг 3: Очистка и подготовка директории на сервере
log "Очистка директории терминала на сервере..."
ssh quer-x0 "sudo /home/<USER>/deploy-scripts/clean-public-terminal.sh $ENVIRONMENT" || error "Не удалось очистить директорию на сервере!"

# Шаг 4: Проверяем наличие директории, временно изменяем владельца директории для копирования
log "Проверка и подготовка директории для копирования..."
# Создаем директорию если её нет
if [[ "$ENVIRONMENT" == "production" ]]; then
  ssh quer-x0 "sudo mkdir -p /var/www/quer.us/current/public/terminal" || error "Не удалось создать директорию на сервере!"
else
  ssh quer-x0 "sudo mkdir -p /var/www/s.quer.us/current/public/terminal" || error "Не удалось создать директорию на сервере!"
fi
ssh quer-x0 "sudo /home/<USER>/deploy-scripts/set-owner-to-x0.sh $ENVIRONMENT" || error "Не удалось изменить владельца директории!"

# Шаг 5: Копирование файлов на сервер (теперь пользователь x0 имеет права на запись)
log "Копирование файлов на сервер..."
# Предварительно удаляем .DS_Store файлы
find build -name ".DS_Store" -delete
if [[ "$ENVIRONMENT" == "production" ]]; then
  scp -r build/* quer-x0:/var/www/quer.us/current/public/terminal/ || error "Не удалось скопировать файлы на сервер!"
else
  scp -r build/* quer-x0:/var/www/s.quer.us/current/public/terminal/ || error "Не удалось скопировать файлы на сервер!"
fi
success "Файлы успешно скопированы на сервер!"

# Шаг 5: Установка правильных прав доступа через централизованную функцию
log "Установка правильных прав доступа через централизованную функцию..."
set_deployment_permissions "${WEB_ROOT}/current" "$ENVIRONMENT"

# Шаг 6: Comprehensive deployment validation
log "Comprehensive deployment integrity validation..."
validate_deployment_integrity "${WEB_ROOT}/current" "$ENVIRONMENT"

# Шаг 7: Перезапуск веб-сервера и бэкенда
log "Перезапуск веб-сервера и бэкенд-сервисов..."
ssh quer-x0 "sudo /home/<USER>/deploy-scripts/restart-services.sh $ENVIRONMENT" || error "Не удалось перезапустить сервисы!"

# Шаг 8: Проверка работоспособности
log "Проверка работоспособности сайта..."
sleep 3 # Даем время на перезапуск сервисов

# Логин и пароль для базовой аутентификации
# Используем переменные окружения если они установлены, иначе используем предустановленные значения
DEPLOY_AUTH_USER="${DEPLOY_AUTH_USER:-$AUTH_USER}"
DEPLOY_AUTH_PASS="${DEPLOY_AUTH_PASS:-$AUTH_PASS}"

# Проверка наличия учетных данных
if [ "$DEPLOY_AUTH_USER" = "unknown" ] || [ "$DEPLOY_AUTH_PASS" = "unknown" ]; then
  error "Deployment credentials not set. Please set DEPLOY_AUTH_USER and DEPLOY_AUTH_PASS environment variables or check script configuration."
fi

# Проверка доступности фронтенда
# Коды 200 (успех) и 403 (требуется авторизация) считаем успешными
if [[ "$ENVIRONMENT" == "production" ]]; then
  # Для production без авторизации
  FRONTEND_STATUS=$(ssh quer-x0 "curl -s -o /dev/null -w '%{http_code}' https://quer.us/terminal/ -k")
else
  # Для staging с авторизацией
  FRONTEND_STATUS=$(ssh quer-x0 "curl -s -o /dev/null -w '%{http_code}' https://s.quer.us/terminal/ -k -u ${DEPLOY_AUTH_USER}:${DEPLOY_AUTH_PASS}")
fi

if [[ $FRONTEND_STATUS -ne 200 && $FRONTEND_STATUS -ne 403 && $FRONTEND_STATUS -ne 401 ]]; then
  error "Терминал недоступен! HTTP-статус: $FRONTEND_STATUS"
else
  success "Терминал доступен (статус: $FRONTEND_STATUS)"
  if [ "$FRONTEND_STATUS" -eq 403 ] || [ "$FRONTEND_STATUS" -eq 401 ]; then
    log "Получен статус авторизации, сайт защищен и работает нормально"
  fi
fi

# Проверка доступности API
if [[ "$ENVIRONMENT" == "production" ]]; then
  API_STATUS=$(ssh quer-x0 "curl -s -o /dev/null -w '%{http_code}' http://localhost:8082/api/calculate -X POST -H 'Content-Type: application/json' -d '{\"entryPrice\":\"100\",\"stopLoss\":\"90\",\"risk\":\"1\",\"accountBalance\":\"10000\"}'")
else
  API_STATUS=$(ssh quer-x0 "curl -s -o /dev/null -w '%{http_code}' http://localhost:8081/api/calculate -X POST -H 'Content-Type: application/json' -d '{\"entryPrice\":\"100\",\"stopLoss\":\"90\",\"risk\":\"1\",\"accountBalance\":\"10000\"}'")
fi

if [ "$API_STATUS" -ne 200 ]; then
  log "API проверка локально вернула статус: $API_STATUS"
  log "Будет выполнена дополнительная проверка через фронтенд..."

  # Дополнительная проверка через публичный API
  if [[ "$ENVIRONMENT" == "production" ]]; then
    PUBLIC_API_STATUS=$(ssh quer-x0 "curl -s -o /dev/null -w '%{http_code}' https://quer.us/api/terminal/calculate -k -X POST -H 'Content-Type: application/json' -d '{\"entryPrice\":\"100\",\"stopLoss\":\"90\",\"risk\":\"1\",\"accountBalance\":\"10000\"}'")
  else
    PUBLIC_API_STATUS=$(ssh quer-x0 "curl -s -o /dev/null -w '%{http_code}' https://s.quer.us/api/terminal/calculate -k -u ${DEPLOY_AUTH_USER}:${DEPLOY_AUTH_PASS} -X POST -H 'Content-Type: application/json' -d '{\"entryPrice\":\"100\",\"stopLoss\":\"90\",\"risk\":\"1\",\"accountBalance\":\"10000\"}'")
  fi

  if [[ $PUBLIC_API_STATUS -ne 200 && $PUBLIC_API_STATUS -ne 403 && $PUBLIC_API_STATUS -ne 401 ]]; then
    error "API недоступен! HTTP-статус: $API_STATUS, публичный статус: $PUBLIC_API_STATUS"
  else
    success "API доступен через фронтенд (статус: $PUBLIC_API_STATUS)"
    if [ "$PUBLIC_API_STATUS" -eq 403 ] || [ "$PUBLIC_API_STATUS" -eq 401 ]; then
      log "Получен статус авторизации для API, сервер работает нормально"
    fi
  fi
else
  success "API доступен напрямую (статус: $API_STATUS)"
fi

success "==========================================="
success "🚀 Деплой терминала успешно завершен!"
if [[ "$ENVIRONMENT" == "production" ]]; then
  success "Терминал доступен по адресу: https://quer.us/terminal/"
else
  success "Терминал доступен по адресу: https://s.quer.us/terminal/"
fi
success "==========================================="