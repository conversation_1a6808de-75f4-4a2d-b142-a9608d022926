# ==============================================================
# ФИНАЛЬНАЯ КОНФИГУРАЦИЯ NGINX v33.0 (Архитектурно-правильная)
# ==============================================================
upstream astro_backend_{{TARGET_ENV}} { server 127.0.0.1:{{ASTRO_PORT}}; }
upstream go_backend_{{TARGET_ENV}} { server 127.0.0.1:{{GO_BACKEND_PORT}}; }
upstream rust_backend_{{TARGET_ENV}} { server 127.0.0.1:{{RUST_BACKEND_PORT}}; }

server {
    listen 80;
    server_name {{DOMAIN_NAME}} www.{{DOMAIN_NAME}};
    return 301 https://\$host\$request_uri;
}
server {
    listen 443 ssl http2;
    server_name {{DOMAIN_NAME}} www.{{DOMAIN_NAME}};

    ssl_certificate /etc/ssl/cloudflare/{{DOMAIN_NAME}}.pem;
    ssl_certificate_key /etc/ssl/cloudflare/{{DOMAIN_NAME}}.key;

    add_header Content-Security-Policy "script-src 'self' 'unsafe-inline' https://clerk.{{DOMAIN_NAME}}; worker-src 'self' blob:; frame-src 'self' https://clerk.{{DOMAIN_NAME}}; object-src 'none'; base-uri 'self';" always;
    
    # --- Маршруты в порядке от наиболее специфичного к наиболее общему ---

    # 1. Прокси для Clerk
    location /__clerk {
        rewrite ^/__clerk/(.*)\$ /\$1 break;
        proxy_pass https://frontend-api.clerk.dev;
        proxy_ssl_server_name on;
        proxy_set_header Host frontend-api.clerk.dev;
        proxy_set_header X-Real-IP \$remote_addr;
    }

    # 2. React Calculator (терминал)
    location /terminal/ {
        alias /var/www/{{DOMAIN_NAME}}/{{TARGET_ENV}}/public/terminal/;
        try_files \$uri \$uri/ /terminal/index.html;
    }

    # 3. API для Rust
    location /api/terminal/calculate {
        proxy_pass http://rust_backend_{{TARGET_ENV}};
        proxy_set_header Host \$server_name;
        proxy_set_header X-Real-IP \$remote_addr;
    }

    # 4. API для Go
    location /api/landing/ {
        # Удаляем /landing/ из пути перед отправкой на бэкенд
        rewrite ^/api/landing/(.*)\$ /api/\$1 break;
        proxy_pass http://go_backend_{{TARGET_ENV}};
        proxy_set_header Host \$server_name;
        proxy_set_header X-Real-IP \$remote_addr;
    }
    
    # 5. Все остальные запросы на Astro
    location / {
        proxy_pass http://astro_backend_{{TARGET_ENV}};
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
    }
}